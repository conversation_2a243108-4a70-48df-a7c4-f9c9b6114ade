# Repository Guidelines

## Project Structure & Module Organization
- `apps/`: Django apps (e.g., `accounts/`). Place app-specific tests in `tests.py` or `tests/`.
- `core/`: Project config (`settings.py`, `urls.py`, `wsgi.py`, `asgi.py`).
- `templates/`: Global/shared templates only (e.g., `templates/base.html`, `templates/index.html`).
- `static/`: Assets; Tailwind compiles `static/css/source.css` to `static/css/main.css`.
- Root tools: `manage.py`, `Makefile`, `compose.yml`, `pyproject.toml`, `ruff.toml`, `.pre-commit-config.yaml`.

### App templates rule
- App-related templates must live under the app’s own template folder using an app namespace to avoid collisions:
  - Path pattern: `apps/<app>/templates/<app>/<template>.html`
  - Views should reference them with the namespaced path, e.g. `template_name = "accounts/login.html"`.
- Reserve project-level `templates/` for shared base layouts, components, and truly global pages (e.g., `base.html`, `index.html`).

## Frontend UI & Styling (daisyUI + Tailwind)
- Use daisyUI components with Tailwind CSS for ALL UI going forward. Do not introduce other CSS frameworks or component libraries.
- Tailwind is wired via `django-tailwind-cli` with daisyUI enabled:
  - Source CSS: `static/css/source.css` (contains `@import "tailwindcss";` and `@plugin "daisyui";`).
  - Built CSS: `static/css/main.css` (injected via `{% tailwind_css %}` in templates).
- New pages must extend `templates/base.html` and use daisyUI classes (e.g., `navbar`, `btn`, `card`, `form-control`, `alert`, `menu`).
- Theme: default `dracula`. Use daisyUI `theme-controller` for toggles; theme is persisted in `localStorage` under key `theme` (already handled in `templates/base.html`).
- Prefer Tailwind utility classes; only use `@apply` for small, shared patterns. If adding custom styles, place them under `@layer components` in `static/css/source.css`.
- App-specific templates still follow the app namespace rule above; global/shared UI lives in project-level `templates/`.

## Build, Test, and Development Commands
- `make init` — bootstrap dev: start Docker, create DB, install deps, migrate, setup Tailwind.
- `make dev` — run Django dev server with Tailwind watch at `0.0.0.0:8000`.
- `make test` — run the Django test suite.
- `uv run manage.py migrate` — apply DB migrations.
- `make lint` — Ruff lint + format.
- `make tunnel` — start Cloudflare tunnel for external access.
- `make clear` — stop containers and remove volumes (reset DB).

## Coding Style & Naming Conventions
- Python 3.13, Django 5.2. Use `uv` for running and dependencies.
- Ruff enforces: line length 88, 2-space indentation, double quotes, import sorting.
- Names: modules `snake_case`, classes `PascalCase`, constants `UPPER_SNAKE_CASE`.
- Django apps are lowercase (e.g., `accounts`). Avoid cross-app coupling; import via public interfaces.
- Do not use `from __future__ import annotations` in new or updated code.

## Testing Guidelines
- Use Django’s `TestCase`/`SimpleTestCase`. Tests live in `apps/<app>/tests.py` or `apps/<app>/tests/test_*.py`.
- Run all tests: `uv run manage.py test`. Per app: `uv run manage.py test accounts`.
- Add tests for new models, views, and URL routes. Include minimal fixtures in test modules.

## Commit & Pull Request Guidelines
- Commits follow Conventional style seen in history: `feat: ...`, `fix: ...`, `chore: ...`, `docs: ...`, `refactor: ...`, `test: ...`.
- Keep subjects imperative and concise; include context in the body when needed.
- PRs: clear description, linked issues, steps to verify, and screenshots for UI changes. Note migrations if required.

## Security & Configuration
- Do not commit secrets. Configure via env vars consumed by `core/settings.py`: `PAYKKA_DUTY_DB_NAME|USER|PASSWORD|HOST|PORT`.
- Set `SECRET_KEY` for production and restrict `ALLOWED_HOSTS`.
- `compose.yml` exposes Postgres and a Keep API for local development only.

## Architecture Notes
- Custom user model is `accounts.User` (`AUTH_USER_MODEL`). Use `settings.AUTH_USER_MODEL` in relations.
- New features should live in new or existing apps under `apps/`. Keep views thin and move logic into services/models where appropriate.
