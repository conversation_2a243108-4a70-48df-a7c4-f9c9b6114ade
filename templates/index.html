{% extends "base.html" %}

{% block title %}首页{% endblock %}

{% block content %}
<section class="hero min-h-[60svh] bg-base-200 rounded-box">
  <div class="hero-content text-center">
    <div class="max-w-2xl">
      <h1 class="text-5xl font-bold">PayKKa Duty</h1>
      <p class="py-6 opacity-80">
        一个简单高效的排班/值班管理系统。使用 Tailwind CSS + daisyUI 构建的现代化界面。
      </p>
      {% if user.is_authenticated %}
      <a href="#" class="btn btn-primary">进入控制台</a>
      {% else %}
      <a href="{% url 'accounts:login' %}" class="btn btn-primary">登录</a>
      <a href="#" class="btn btn-ghost">了解更多</a>
      {% endif %}
    </div>
  </div>
  </section>

<div class="mt-8 grid gap-4 sm:grid-cols-2">
  <div class="card bg-base-100 border">
    <div class="card-body">
      <h2 class="card-title">快速上手</h2>
      <p>使用导航栏中的“主题”切换浅色/深色，开始体验 daisyUI 组件。</p>
      <div class="card-actions justify-end">
        <a href="#" class="btn btn-outline btn-sm">文档</a>
      </div>
    </div>
  </div>
  <div class="card bg-base-100 border">
    <div class="card-body">
      <h2 class="card-title">HTMX 支持</h2>
      <p>模板已集成 HTMX，可逐步增强交互体验。</p>
      <div class="card-actions justify-end">
        <a href="#" class="btn btn-outline btn-sm">示例</a>
      </div>
    </div>
  </div>
  </div>
{% endblock %}
