{% load static tailwind_cli django_htmx %}

<!DOCTYPE html>
<html lang="zh-CN" data-theme="dracula">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}{% endblock %} - PayKKa Duty</title>
  {% tailwind_css %}
  {% htmx_script %}
  <script>
    (function () {
      const key = "theme";
      const doc = document.documentElement;
      const saved = localStorage.getItem(key);
      if (saved) doc.setAttribute("data-theme", saved);
      document.addEventListener("change", function (e) {
        const t = e.target;
        if (t && t.classList && t.classList.contains("theme-controller")) {
          const val = t.value;
          if (val) {
            doc.setAttribute("data-theme", val);
            localStorage.setItem(key, val);
          }
        }
      });
    })();
  </script>
  <link rel="icon" href="{% static 'favicon.ico' %}">
  <meta name="color-scheme" content="light dark">
  <meta name="theme-color" content="#181920">
  <meta name="description" content="PayKKa Duty">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="PayKKa Duty">
  <meta name="format-detection" content="telephone=no">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="HandheldFriendly" content="true">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
</head>

<body hx-headers='{"x-csrftoken": "{{ csrf_token }}"}' class="min-h-svh flex flex-col">
  <header class="navbar bg-base-100 border-b">
    <div class="flex-1">
      <a href="{% url 'index' %}" class="btn btn-ghost text-xl">PayKKa Duty</a>
    </div>
    <div class="flex-none gap-2">
      <div class="dropdown dropdown-end">
        <div tabindex="0" role="button" class="btn btn-ghost">主题</div>
        <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-44 p-2 shadow">
          <li>
            <label class="label cursor-pointer justify-between">
              <span class="label-text">明亮</span>
              <input type="radio" name="theme" aria-label="Light" value="latte" class="theme-controller radio" />
            </label>
          </li>
          <li>
            <label class="label cursor-pointer justify-between">
              <span class="label-text">暗黑</span>
              <input type="radio" name="theme" aria-label="Dark" value="mocha" class="theme-controller radio" />
            </label>
          </li>
        </ul>
      </div>
      {% if user.is_authenticated %}
      <div class="px-2 text-sm opacity-80">欢迎，{{ user.get_username }}</div>
      {% else %}
      <a href="{% url 'accounts:login' %}" class="btn btn-primary btn-sm">登录</a>
      {% endif %}
    </div>
  </header>

  <main class="container max-w-7xl mx-auto w-full grow px-4 py-6">
    {% block content %}{% endblock %}
  </main>

  <footer class="footer footer-center bg-base-200 text-base-content p-6">
    <aside>
      <p class="text-sm opacity-70">© {{ now|date:"Y" }} PayKKa Duty · All rights reserved.</p>
    </aside>
  </footer>
</body>

</html>