services:
  db:
    image: postgres:17.6-alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: keep
    healthcheck:
      test: ["CMD-SHELL", "sh -c 'pg_isready -U postgres -d postgres'"]
      interval: 10s
      timeout: 3s
      retries: 3

  keep:
    image: us-central1-docker.pkg.dev/keephq/keep/keep-api:0.47.9
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "8080:8080"
    environment:
      AUTH_TYPE: DB
      SECRET_MANAGER_TYPE: DB
      DATABASE_CONNECTION_STRING: **************************************/keep
      DB_NAME: keep
      PUSHER_DISABLED: true
      KEEP_JWT_SECRET: simplesecret
      KEEP_DEFAULT_USERNAME: admin
      KEEP_DEFAULT_PASSWORD: admin
      KEEP_DEFAULT_API_KEYS: "admin:admin:mysecretkey"
