{% extends "base.html" %}

{% block title %}登录{% endblock %}

{% block content %}
<section class="min-h-svh flex items-center justify-center p-6">
  <div class="w-full max-w-sm">
    <h1 class="text-2xl font-bold mb-6 text-center">登录</h1>
    <form method="post" novalidate class="space-y-4">
      {% csrf_token %}

      {% if form.non_field_errors %}
      <div class="alert alert-error text-sm">
        {{ form.non_field_errors }}
      </div>
      {% endif %}

      <div class="form-control">
        <label class="label" for="id_email"><span class="label-text">邮箱</span></label>
        <input type="email" name="email" id="id_email" value="{{ form.email.value|default_if_none:'' }}" class="input input-bordered w-full" required />
        {% if form.email.errors %}
        <label class="label"><span class="label-text-alt text-error">{{ form.email.errors|join:", " }}</span></label>
        {% endif %}
      </div>

      <div class="form-control">
        <label class="label" for="id_password"><span class="label-text">密码</span></label>
        <input type="password" name="password" id="id_password" class="input input-bordered w-full" required />
        {% if form.password.errors %}
        <label class="label"><span class="label-text-alt text-error">{{ form.password.errors|join:", " }}</span></label>
        {% endif %}
      </div>

      {% if next %}
      <input type="hidden" name="next" value="{{ next }}" />
      {% endif %}

      <button type="submit" class="btn btn-primary w-full">登录</button>
    </form>

    <div class="text-center mt-4">
      <a href="/" class="link">返回首页</a>
    </div>
  </div>
  </section>
{% endblock %}

