from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse


class EmailLoginTests(TestCase):
  def setUp(self):
    User = get_user_model()
    self.password = "p@ssw0rd123"
    self.user = User.objects.create_user(
      username="jdoe",
      password=self.password,
      email="<EMAIL>",
      userid="u-123",
      name="<PERSON>",
      mobile="***********",
    )

  def test_get_login_page(self):
    url = reverse("accounts:login")
    res = self.client.get(url)
    self.assertEqual(res.status_code, 200)
    self.assertContains(res, "邮箱")

  def test_login_with_email_success(self):
    url = reverse("accounts:login")
    res = self.client.post(
      url, {"email": "<EMAIL>", "password": self.password}, follow=True
    )
    self.assertEqual(res.status_code, 200)
    self.assertTrue(res.wsgi_request.user.is_authenticated)
    self.assertEqual(res.wsgi_request.user.pk, self.user.pk)

  def test_login_with_invalid_credentials(self):
    url = reverse("accounts:login")
    res = self.client.post(url, {"email": "<EMAIL>", "password": "wrong"})
    self.assertEqual(res.status_code, 200)
    self.assertFalse(res.wsgi_request.user.is_authenticated)
    self.assertContains(res, "correct email and password")
