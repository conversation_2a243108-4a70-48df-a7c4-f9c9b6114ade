from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend


class EmailBackend(ModelBackend):
  """Authenticate users by email and password.

  Accepts either `email` via kwargs or treats `username` as an email string.
  Falls back gracefully by returning None if user not found or password invalid.
  """

  def authenticate(self, request, username=None, password=None, **kwargs):  # type: ignore[override]
    User = get_user_model()

    # Prefer explicit `email` kwarg; otherwise assume `username` may be an email.
    lookup = kwargs.get("email") or username
    if not lookup or password is None:
      return None

    try:
      user = User.objects.get(email__iexact=lookup)
    except User.DoesNotExist:
      return None

    if user.check_password(password) and self.user_can_authenticate(user):
      return user
    return None
