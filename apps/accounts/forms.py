from django import forms
from django.contrib.auth import authenticate
from django.utils.translation import gettext_lazy as _


class EmailAuthenticationForm(forms.Form):
  email = forms.EmailField(label=_("Email"))
  password = forms.CharField(
    label=_("Password"), widget=forms.PasswordInput, strip=False
  )

  error_messages = {
    "invalid_login": _("Please enter a correct email and password."),
    "inactive": _("This account is inactive."),
  }

  def __init__(self, request=None, *args, **kwargs):
    self.request = request
    self.user_cache = None
    super().__init__(*args, **kwargs)

  def clean(self):
    cleaned = super().clean()
    email = cleaned.get("email")
    password = cleaned.get("password")
    if email and password:
      self.user_cache = authenticate(self.request, email=email, password=password)
      if self.user_cache is None:
        raise forms.ValidationError(
          self.error_messages["invalid_login"], code="invalid_login"
        )
      self.confirm_login_allowed(self.user_cache)
    return cleaned

  def confirm_login_allowed(self, user):
    if not user.is_active:
      raise forms.ValidationError(self.error_messages["inactive"], code="inactive")

  def get_user(self):
    return self.user_cache
