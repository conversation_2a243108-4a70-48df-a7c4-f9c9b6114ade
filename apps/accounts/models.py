from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class Gender(models.IntegerChoices):
  UNKNOWN = (0, _("Unknown"))
  MALE = (1, _("Male"))
  FEMALE = (2, _("Female"))


class User(AbstractUser):
  userid = models.CharField(max_length=128, unique=True, verbose_name=_("User ID"))
  name = models.Char<PERSON>ield(max_length=128, verbose_name=_("Name"))
  alias = models.Char<PERSON>ield(
    max_length=128, null=True, blank=True, verbose_name=_("Alias")
  )

  gender = models.SmallIntegerField(
    choices=Gender.choices, default=Gender.UNKNOWN, verbose_name=_("Gender")
  )
  email = models.EmailField(max_length=128, unique=True, verbose_name=_("Email"))
  mobile = models.CharField(max_length=128, unique=True, verbose_name=_("Mobile"))
  avatar = models.U<PERSON><PERSON><PERSON>(null=True, blank=True, verbose_name=_("Avatar"))
